#!/bin/bash

# 槽位抽取服务打包脚本 - 使用git归档功能，确保单个zip不超过100KB
# 支持完整包和增量包两种模式

# 设置变量
PROJECT_NAME="slots_extra"
OUTPUT_DIR="./dist"
TEMP_DIR="./temp_package"
MAX_SIZE=100 # 最大大小（KB）
SPLIT_SIZE=100 # 拆分大小（KB）

# 创建必要的目录
mkdir -p "$OUTPUT_DIR"
mkdir -p "$TEMP_DIR"

# 清理旧的打包文件
rm -rf "$OUTPUT_DIR"/*
rm -rf "$TEMP_DIR"/*

# 函数：处理单个zip文件的打包和拆分
process_zip_file() {
    local source_file="$1"
    local output_name="$2"
    local description="$3"

    echo "正在处理${description}..."

    # 获取归档文件大小（KB）
    ARCHIVE_SIZE=$(du -k "$source_file" | cut -f1)
    echo "${description}大小: ${ARCHIVE_SIZE}KB"

    # 判断是否需要拆分
    if [ "$ARCHIVE_SIZE" -le "$MAX_SIZE" ]; then
        # 如果文件小于限制大小，直接移动到输出目录
        echo "${description}小于${MAX_SIZE}KB，无需拆分"
        mv "$source_file" "$OUTPUT_DIR/${output_name}.zip"
    else
        # 如果文件大于限制大小，使用split命令拆分并添加.zip扩展名
        echo "${description}大于${MAX_SIZE}KB，需要拆分"
        echo "正在拆分文件..."

        # 确保输出目录存在
        mkdir -p "$OUTPUT_DIR"

        # 计算拆分大小（字节）
        SPLIT_BYTES=$((SPLIT_SIZE * 1024))

        # 拆分zip文件到临时目录
        cd "$TEMP_DIR"
        split -b $SPLIT_BYTES "$(basename "$source_file")" "${output_name}_part_"
        cd - > /dev/null

        # 重命名拆分文件，添加.zip扩展名，并移动到输出目录
        part_counter=1
        for part_file in "$TEMP_DIR"/${output_name}_part_*; do
            if [ -f "$part_file" ]; then
                # 生成有序的文件名：part01.zip, part02.zip, ...
                new_name="${output_name}_part$(printf "%02d" $part_counter).zip"
                mv "$part_file" "$OUTPUT_DIR/$new_name"
                echo "  创建: $new_name"
                ((part_counter++))
            fi
        done

        # 删除原始大文件
        rm "$source_file"

        echo "文件已拆分为 $((part_counter-1)) 个部分，每个部分带.zip扩展名"
        echo "使用方法: cat ${output_name}_part*.zip > ${output_name}_merged.zip && unzip ${output_name}_merged.zip"
    fi
}



echo "=== 槽位抽取服务打包流程开始 ==="

# 检查是否在git仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "错误: 当前目录不是git仓库"
    exit 1
fi

# 1. 创建完整包
echo "1. 正在创建完整包..."
# 使用git归档功能创建一个完整的归档文件
# 这会自动排除.gitignore中指定的文件
git archive --format=zip -o "$TEMP_DIR/${PROJECT_NAME}_full.zip" HEAD

# 处理完整包
process_zip_file "$TEMP_DIR/${PROJECT_NAME}_full.zip" "${PROJECT_NAME}_full" "完整包"

# 2. 创建增量包
echo ""
echo "2. 正在创建增量包..."

# 检查是否有足够的提交历史来创建增量包
COMMIT_COUNT=$(git rev-list --count HEAD)
if [ "$COMMIT_COUNT" -lt 2 ]; then
    echo "警告: 仓库中只有一个提交，无法创建增量包"
else
    # 获取最新提交的变更文件列表
    CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)

    if [ -z "$CHANGED_FILES" ]; then
        echo "最新提交没有文件变更，跳过增量包创建"
    else
        echo "最新提交变更的文件:"
        echo "$CHANGED_FILES" | sed 's/^/  - /'

        # 创建增量包，只包含变更的文件
        # 使用临时文件存储文件列表
        echo "$CHANGED_FILES" > "$TEMP_DIR/changed_files.txt"

        # 过滤出实际存在的文件
        EXISTING_FILES=""
        while IFS= read -r file; do
            if [ -f "$file" ]; then
                EXISTING_FILES="$EXISTING_FILES $file"
            else
                echo "  警告: 文件 $file 不存在，跳过"
            fi
        done <<< "$CHANGED_FILES"

        if [ -n "$EXISTING_FILES" ]; then
            # 使用git archive创建增量包，只包含存在的文件
            git archive --format=zip -o "$TEMP_DIR/${PROJECT_NAME}_incremental.zip" HEAD $EXISTING_FILES

            # 检查增量包是否创建成功
            if [ -f "$TEMP_DIR/${PROJECT_NAME}_incremental.zip" ]; then
                # 处理增量包
                process_zip_file "$TEMP_DIR/${PROJECT_NAME}_incremental.zip" "${PROJECT_NAME}_incremental" "增量包"
            else
                echo "增量包创建失败"
            fi
        else
            echo "没有有效的变更文件，跳过增量包创建"
        fi

        # 清理临时文件
        rm -f "$TEMP_DIR/changed_files.txt"
    fi
fi



# 显示结果
echo ""
echo "=== 打包完成！ ==="
echo "文件保存在 $OUTPUT_DIR 目录中"

# 列出创建的zip文件及其大小
echo ""
echo "创建的文件列表:"
if [ -d "$OUTPUT_DIR" ] && [ "$(ls -A "$OUTPUT_DIR" 2>/dev/null)" ]; then
    ls -lh "$OUTPUT_DIR" | grep -v '^total' | while read -r line; do
        filename=$(echo "$line" | awk '{print $NF}')
        size=$(echo "$line" | awk '{print $5}')

        if [[ "$filename" == *"_full"* ]]; then
            echo "  📦 完整包: $filename ($size)"
        elif [[ "$filename" == *"_incremental"* ]]; then
            echo "  📈 增量包: $filename ($size)"
        elif [[ "$filename" == *"_part_"* ]]; then
            echo "  📂 拆分包: $filename ($size)"
        else
            echo "  📄 其他: $filename ($size)"
        fi
    done
else
    echo "  没有创建任何文件"
fi

# 显示使用说明
echo ""
echo "=== 使用说明 ==="
echo "📦 完整包: 包含所有项目文件（除.gitignore忽略的文件）"
echo "📈 增量包: 仅包含最新提交的变更文件"
echo "📂 拆分包: 当文件超过${MAX_SIZE}KB时自动拆分的文件"

# 清理临时文件
rm -rf "$TEMP_DIR"

echo ""
echo "槽位抽取服务打包流程完成！"
