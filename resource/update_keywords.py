import json
import os
import re
from typing import Dict, List
from update_helper import update_patterns_to_es

KEYWORDS_FILE_PATH = os.path.join(os.path.dirname(__file__), "keywords", "keywords.json")

def _convert_keywords_to_regex(keywords: List[str]) -> str:
    """
    将关键词列表转换为正则表达式字符串
    
    Args:
        keywords: 关键词列表
        
    Returns:
        str: 正则表达式字符串
    """
    if not keywords:
        return r"(?!)"  # 返回一个不可能匹配的模式
        
    # 对关键词进行转义
    escaped_keywords = [re.escape(keyword) for keyword in keywords]
    
    # 使用|连接所有关键词模式构成或关系的正则表达式
    # 为了提高匹配效率和准确性，对较长的词优先匹配，并添加边界符
    # 按长度降序排序
    escaped_keywords.sort(key=len, reverse=True)
    pattern_str = "|".join(escaped_keywords)
    
    return pattern_str

def load_and_compile_keywords() -> Dict[str, re.Pattern]:
    """
    加载keywords.json，将关键词列表转换为正则表达式并编译
    
    Returns:
        Dict[str, re.Pattern]: 编译后的正则表达式字典，键为 "slot_value-slot_name"
    """
    compiled_patterns: Dict[str, re.Pattern] = {}
    
    try:
        with open(KEYWORDS_FILE_PATH, 'r', encoding='utf-8') as f:
            keywords_data = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到关键词文件 {KEYWORDS_FILE_PATH}")
        return {}
    except json.JSONDecodeError:
        print(f"错误：解析关键词文件 {KEYWORDS_FILE_PATH} 失败")
        return {}
    except Exception as e:
        print(f"加载关键词文件时发生未知错误: {str(e)}")
        return {}

    print("开始处理关键词并编译为正则表达式...")
    total_count = 0
    compile_errors = 0
    
    for slot_name, slot_values in keywords_data.items():
        for slot_value, words in slot_values.items():
            total_count += 1
            # 1. 将关键词列表转换为正则表达式字符串
            regex_pattern_str = _convert_keywords_to_regex(words)
            
            # 2. 尝试编译正则表达式以验证其正确性
            try:
                compiled_pattern = re.compile(regex_pattern_str)
                # "slot_value-slot_name" 作为键，与 helper 函数中的解析逻辑保持一致
                pattern_id = f"{slot_value}-{slot_name}" 
                compiled_patterns[pattern_id] = compiled_pattern
                print(f"成功编译: {pattern_id}") 
            except re.error as e:
                compile_errors += 1
                print(f"错误：编译 {slot_name} - {slot_value} 的正则表达式失败: {str(e)}")
                print(f"  生成的正则表达式: {regex_pattern_str}")
            except Exception as e:
                compile_errors += 1
                print(f"编译 {slot_name} - {slot_value} 时发生未知错误: {str(e)}")

    print(f"关键词处理完成。总计处理 {total_count} 个槽位值，编译成功 {len(compiled_patterns)} 个，失败 {compile_errors} 个。")
    
    return compiled_patterns

if __name__ == "__main__":
    print("开始更新关键词到ES...")
    # 1. 加载并编译关键词
    compiled_keyword_patterns = load_and_compile_keywords()
    
    # 2. 如果有成功编译的模式，则调用更新函数
    if compiled_keyword_patterns:
        update_patterns_to_es(compiled_keyword_patterns, data_type="keywords")
    else:
        print("没有成功编译的关键词正则表达式，无需更新。")
    
    print("关键词更新脚本执行完毕。")
