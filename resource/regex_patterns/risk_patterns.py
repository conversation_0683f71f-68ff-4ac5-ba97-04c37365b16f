import re

RISK_PREFIX = "risk"

# 风险范围连接词
# 按符号类型分组
RANGE_TEXT_CONNECTORS = r'到|至|和|与|或|及'
RANGE_DASH_CONNECTORS = r'-|—|－|–|‐|‑|‒|⸺|⸻|﹘|﹣|⹀'
RANGE_TILDE_CONNECTORS = r'~|⁓|∼|～|∿|〜|〰'
RANGE_ARROW_CONNECTORS = r'→|➝|➞|⟶|⇒|⟹|⇾|⟾|⟿'
RANGE_OTHER_SYMBOLS = r'、|_|⁃|…|=|==|:|\\+|±'  # 转义加号
RANGE_COMPOUND_SYMBOLS = r'->|-->|=>'

# 风险范围连接词
RISK_RANGE_CONNECTOR = r'(' + '|'.join([
    RANGE_TEXT_CONNECTORS,
    RANGE_DASH_CONNECTORS,
    RANGE_TILDE_CONNECTORS,
    RANGE_ARROW_CONNECTORS,
    RANGE_OTHER_SYMBOLS,
    RANGE_COMPOUND_SYMBOLS
]) + r')'

# 近似修饰前缀：出现在风险等级前的近似修饰词
PREFIX_RISK_APPROXIMATION = r'大约|大概|约|约为|粗略|大致|大致上|大体|概|粗略估计|姑且|权且|暂且'
PREFIX_RISK_PROXIMITY = r'接近|差不多|近|将近|几乎|接近于|濒临|靠近|邻近|左近|附近'
PREFIX_RISK_ESTIMATION = r'估计|预期|预计|预测|目标|粗算|算下来|看起来|算起来|目测|估摸|估量|佔计|目估|初步估计'
PREFIX_RISK_PREDICTION = r'预计|预估|预期|预测|可能|或许|也许|兴许|恐怕|大抵|想来|想必|料想|多半'
PREFIX_RISK_EQUIVALENCE = r'相当于|等同于|等于|等价于|好比|好像|仿佛|宛如|比如|相当于是|大约等于'
PREFIX_RISK_UNCERTAINTY = r'也就|大体上|基本|基本上|大体来说|总体来讲|笼统地说|大略|凡是|大体而言'

RISK_APPROXIMATE_PREFIX = r'(' + '|'.join([
    PREFIX_RISK_APPROXIMATION,
    PREFIX_RISK_PROXIMITY,
    PREFIX_RISK_ESTIMATION,
    PREFIX_RISK_PREDICTION,
    PREFIX_RISK_EQUIVALENCE,
    PREFIX_RISK_UNCERTAINTY
]) + r')'

# 近似修饰后缀：出现在风险等级后的近似修饰词
SUFFIX_RISK_RANGE = r'左右|上下|内外|之间|前后|区间|附近|范围|浮动|波动'
SUFFIX_RISK_FLUCTUATION = r'上下浮动|摆布|上下波动|起伏|波动|变动|不稳定|波动区间'
SUFFIX_RISK_UNCERTAINTY = r'不等|不定|变化|不一|不确定|待定|未定|不稳定|浮动'
SUFFIX_RISK_EXCESS = r'出头|出头儿|开外|多一点|有余|略多|稍多|略微超过|略超|略高|偏多'
SUFFIX_RISK_APPROXIMATION = r'几|许|多|大概|大约|估计|约摸|预计|或者说|可以说|要么|要不|姑且算是'

RISK_APPROXIMATE_SUFFIX = r'(' + '|'.join([
    SUFFIX_RISK_RANGE,
    SUFFIX_RISK_FLUCTUATION,
    SUFFIX_RISK_UNCERTAINTY,
    SUFFIX_RISK_EXCESS,
    SUFFIX_RISK_APPROXIMATION
]) + r')'

# 上限前缀修饰词：放在风险等级前，表示风险等级的最大限制
UPPER_LIMIT_RISK_NEGATION = r'不超过|不到|不足|不会超过|不高于|不会多于|不能超过|不得超过|不应超过|不可超过|不能高于|不超過|不会超過|不高於|不会多於|不能超過|不应高于'
UPPER_LIMIT_RISK_COMPARISON = r'少于|低于|小于|少於|低於|小於'
UPPER_LIMIT_RISK_MAX = r'最多|顶多|頂多|至多|最高|最大|最高不超过|最大不超|最多不超过|最高不超|最大不超过|封顶|最高风险|最大风险'
UPPER_LIMIT_RISK_BOUNDARY = r'上限是|上限为|封顶|最高限额|最大值为|最高风险等级|最大风险等级'
UPPER_LIMIT_RISK_SYMBOLS = r'≤|<=|≦|<|＜'

RISK_UPPER_LIMIT_PREFIX = r'(' + '|'.join([
    UPPER_LIMIT_RISK_NEGATION,
    UPPER_LIMIT_RISK_COMPARISON,
    UPPER_LIMIT_RISK_MAX,
    UPPER_LIMIT_RISK_BOUNDARY,
    UPPER_LIMIT_RISK_SYMBOLS
]) + r')'

# 上限后缀修饰词：放在风险等级后，表示风险等级的最大限制
SUFFIX_RISK_BOUNDARY = r'以下|以内|之内|及以下|及以内|及之内|未满|之前|以前|不到|顶|为止|为限|止|之下|往下|偏下|向下|范围内|上限'
SUFFIX_RISK_CAP = r'封顶|封顶线|封顶值|最高风险|最大风险|最高风险等级|最大风险等级'
SUFFIX_RISK_COMPARISON = r'或更少|及更少|或以下|及以下|或更低|及更低|或少于此数|更低|更少|不超|最多|最高|最大'
SUFFIX_RISK_RESTRICTION = r'限制|上限|天花板|最高值|最大值|不会更高|上限值'
SUFFIX_RISK_SYMBOLS = r'↓'

RISK_UPPER_LIMIT_SUFFIX = r'(' + '|'.join([
    SUFFIX_RISK_BOUNDARY,
    SUFFIX_RISK_CAP,
    SUFFIX_RISK_COMPARISON,
    SUFFIX_RISK_RESTRICTION,
    SUFFIX_RISK_SYMBOLS
]) + r')'

# 下限前缀修饰词：放在风险等级前，表示风险等级的最小限制
LOWER_LIMIT_RISK_MINIMUM = r'最少|至少|起码|最低|至少要|底线|保底|最低限度|起码有'
LOWER_LIMIT_RISK_NEGATION = r'不少于|不少於|不低于|不低於|不会少于|不会少於|不能少于|不能少於|不小于|不小於|不得少于|不应少於'
LOWER_LIMIT_RISK_COMPARISON = r'多于|多於|高于|高於|大于|大於|超|超过|超出|多过|远超|高出|大过|大于等于'
LOWER_LIMIT_RISK_BOUNDARY = r'下限是|底线是|起始于|开始于|基准是|基线是|最低风险|最低风险等级'
LOWER_LIMIT_RISK_SYMBOLS = r'>|≥|>=|＞|≧|⩾'

RISK_LOWER_LIMIT_PREFIX = r'(' + '|'.join([
    LOWER_LIMIT_RISK_MINIMUM,
    LOWER_LIMIT_RISK_NEGATION,
    LOWER_LIMIT_RISK_COMPARISON,
    LOWER_LIMIT_RISK_BOUNDARY,
    LOWER_LIMIT_RISK_SYMBOLS
]) + r')'

# 下限后缀修饰词：放在风险等级后，表示风险等级的最小限制
SUFFIX_RISK_BOUNDARY_LOWER = r'以上|之上|及以上|及之上|起|开始|为基础|为底|为下限'
SUFFIX_RISK_FLOOR = r'最少|最低|底线|至少|保底|起码|保障|基准|下限|最低值'
SUFFIX_RISK_DIRECTION = r'偏上|往上|向上|以上区间|以上水平'
SUFFIX_RISK_COMPARISON_LOWER = r'或更多|及更多|或以上|及更高|甚至更高|乃至更多|不止|更高|更多'
SUFFIX_RISK_SYMBOLS_LOWER = r'↑|⬆|➚|↗|⥣|⟰|⇧|⇑'

RISK_LOWER_LIMIT_SUFFIX = r'(' + '|'.join([
    SUFFIX_RISK_BOUNDARY_LOWER,
    SUFFIX_RISK_FLOOR,
    SUFFIX_RISK_DIRECTION,
    SUFFIX_RISK_COMPARISON_LOWER,
    SUFFIX_RISK_SYMBOLS_LOWER
]) + r')'

# 基本描述词
# 偏下
RISK_DOWN = r'(?:下|偏下|偏低|往下)'
# 偏上
RISK_UP = r'(?:上|偏上|偏高|往上)'
# 不算高
RISK_NOT_HIGH = r'(?:不算高|不算大|不算多|不算严重)'

# 风险基本描述词
RISK_LEVEL_DESCRIPTOR = r'(?:等级|风险(?:等级|级别|level|Level|LEVEL)?)'
RISK_BASIC = rf'(?:波动|起伏|浮动|回撤|{RISK_LEVEL_DESCRIPTOR})'
RISK_LOW = r'(?:低|小|少)'
RISK_HIGH = r'(?:高|大|多|巨大|显著|明显|严重|无上限|吓人|恐怖|可怕|没底线)'
RISK_EXTREME = r'(?:超|极|最|很|相当|非常|特别|极|极其|极端|由其|超级|顶级|极度|无比|绝对|完全|彻底|终极|非常非常|特别特别|超级超级)'
RISK_SLIGHTLY = r'(?:较|偏|略|稍|微|比较|有点|略微|轻微|稍微|稍稍|微微|基本|相对|较为|还算|总体|靠近|接近|临近|将近|快到|快要|即将|马上|相对较)'
RISK_ASSET = r'(?:资产|资金|投资|理财)'
RISK_TYPE = r'(?:的|得|型)'
RISK_SAFETY = r'(?:安全|保本|保险|保障|稳妥|放心|无忧|可靠|安心|靠谱|靠得住|妥妥的|万无一失|稳稳的|稳稳当当)'
RISK_STABLE = r'(?:稳定|稳的|稳健|安稳|平稳)'
RISK_CAN = r'(?:可|能|受)'
RISK_CANNOT = r'(?:不|不能|无法|没发|没办法)'
RISK_CONTROLLABLE = r'(?:(?:可|能|受)(?:控|掌握|掌控|约束|控制|管理|承受|接受|容忍))'
RISK_DOWN = r'(?:下|偏下|偏低|往下)'
RISK_UP = r'(?:上|偏上|偏高|往上)'
RISK_MIDDLE_DOWN = rf'(?:(?:适?中等?{RISK_DOWN})|(?:低等?{RISK_UP})|(?:略低|稍低|偏下|偏低|偏弱|不高|不大|不多|不强|接近低|不算高|不算大|不算多|不算强|不算严重))'
RISK_MIDDLE = r'(?:中等?|适中|适度|中性|中档|中规|一般|正常|标准|常规|普通|平均|不高不低|不多不少|刚刚好|恰到好处|居中|中间|处于中间)'
RISK_MIDDLE_UP = rf'(?:(?:适?中等?{RISK_UP})|(?:高等?{RISK_DOWN})|(?:略高|稍高|偏上|偏高|偏强|不低|不小|不少|不弱|接近高|不算低|不算小|不算弱|不算少))'
RISK_IS = r'(?:为|是|达|达到|在|大概|大约|有|保持在|等于|维持在|处于|:|：|：\s*)?\s*'

# R1 风险等级（低风险）: 风险极低，非常安全
# 以"风险"、"波动"、"起伏"等风险相关词为核心
R1_CORE = rf'''
(?:
    (?:
        (?:
            (?:
                零
                |
                (?:
                    {RISK_EXTREME}?
                    {RISK_LOW}
                )
            )
            |
            (?:
                (?:没|没有|无|毫无)
                (?:什么|任何)?
                (?:一?点)?
            )
        )
        的?
        {RISK_BASIC}
    )
    |
    (?:
        {RISK_BASIC}
        (?:
            (?:
                {RISK_EXTREME}?
                {RISK_LOW}
                (?:一?点)?
            )
            |
            (?:
                微乎其微|接近于?零|几乎(?:为零|没有|等于零|可以忽略)
            )
        )
    )
)'''
R1_LEVEL = rf'''
(?:
    (?:
        {RISK_LEVEL_DESCRIPTOR}
        {RISK_IS}
        (?:R1|r1|1|一)
        级?
        的?
    )
    |
    (?:
        (?:R1|r1)
        {RISK_LEVEL_DESCRIPTOR}?
    )
)'''
# 稳定安全描述
R1_STABLE_DESCRIPTOR = rf'{RISK_SAFETY}{RISK_TYPE}?'
# 稳赚不亏描述
R1_PROFIT_DESCRIPTOR = r'(?:不舍|不亏|保证)本金|稳赚|不亏|不会亏损|稳赚不赔|稳赢|稳赚|躺赢|躺赚'
# 特殊表达
R1_SPECIAL = r'放心睡大觉|不用操心|省心省力|放着不管也行|睡觉都能赚钱|放着就行|坐等收益|无脑投资|抗通胀|跑赢通胀|适合养老|养老型|低档理财|基础理财|入门级投资|适合小白|新手友好|初级风险|起步型|本金安全优先|收益稳定优先|跟着感觉走|无需经验|看得见摸得着|睡眠质量好'

# 组合R1所有模式
R1_RISK_LEVEL_PATTERN = r'(?:' + '|'.join([
    R1_CORE,
    R1_LEVEL,
    R1_STABLE_DESCRIPTOR,
    R1_PROFIT_DESCRIPTOR,
    R1_SPECIAL
]) + r')'

# R2 风险等级（中低风险）：风险较低，相对安全
# 以"风险"、"波动"、"起伏"等风险相关词为核心
R2_CORE = fr'''
(?:
    (?:
        {RISK_SLIGHTLY}
        {RISK_LOW}
        的?
        {RISK_BASIC}
    )
    |
    (?:
        {RISK_LOW}
        (?:一?点|一?些)
        的?
        {RISK_BASIC}
    )
    |
    (?:
        {RISK_BASIC}
        {RISK_SLIGHTLY}
        {RISK_LOW}
    )
    |
    (?:
        {RISK_BASIC}
        {RISK_LOW}
        (?:一?点|一?些)
    )
    |
    (?:
        {RISK_MIDDLE_DOWN}
        的?
        {RISK_BASIC}
    )
    |
    (?:
        {RISK_BASIC}
        {RISK_MIDDLE_DOWN}
    )
    |
    (?:
        {RISK_BASIC}
        {RISK_CONTROLLABLE}
    )
)'''
R2_LEVEL = rf'''
(?:
    (?:
        {RISK_LEVEL_DESCRIPTOR}
        {RISK_IS}
        (?:R2|r2|2|二)
        级?
        的?
    )
    |
    (?:
        (?:R2|r2)
        {RISK_LEVEL_DESCRIPTOR}?
    )
)'''
# 稳健可控描述
R2_STABLE_DESCRIPTOR = rf'''
(?:
    (?:
        {RISK_SLIGHTLY}?{RISK_STABLE}
        |
        {RISK_SLIGHTLY}{RISK_SAFETY}
    )
    {RISK_TYPE}?
)
'''
# 稳健资产描述
R2_ASSET_DESCRIPTOR = rf'''
(?:
    (?:
    {RISK_ASSET}?
    {RISK_SAFETY}
    )
    {RISK_TYPE}?
)'''
# 特殊表达
R2_SPECIAL = r'稳健打底|有底线的|可预见的|温和增长|略高于存款|略胜余存款|小资金可投|存款替代品|定期存款替代'

# 组合R2所有模式
R2_RISK_LEVEL_PATTERN = r'(?:' + '|'.join([
    R2_CORE,
    R2_LEVEL,
    R2_STABLE_DESCRIPTOR,
    R2_ASSET_DESCRIPTOR,
    R2_SPECIAL
]) + r')'

# R3 风险等级（中风险）：风险适中，需要谨慎
# 以"风险"、"波动"、"起伏"等风险相关词为核心
R3_CORE = rf'''
(?:
    (?:
        {RISK_MIDDLE}
        的?
        {RISK_BASIC}
    )
    |
    (?:
        {RISK_BASIC}
        {RISK_MIDDLE}
    )
)'''
R3_LEVEL = rf'''
(?:
    (?:
        {RISK_LEVEL_DESCRIPTOR}
        {RISK_IS}
        (?:R3|r3|3|三)
        级?
        的?
    )
    |
    (?:
        (?:R3|r3)
        {RISK_LEVEL_DESCRIPTOR}?
    )
)'''
# 平衡型描述
R3_BALANCE_DESCRIPTOR = rf'(?:平衡|均衡|中庸|折中|中和|适中|中规中矩|不偏不倚|进退有度|进退平衡|攻守兼备|进可攻退可守|不咸不淡|不冷不热|不温不火|不高不低|半斤八两|不上不下)(?:{RISK_TYPE})?'
# 收益风险平衡描述
R3_RISK_RETURN_DESCRIPTOR = rf'(?:收益适中|回报适中|回报一般|收益和风险匹配|风险收益对等|风险对等回报|收益对等风险|中等回报|收益风险平衡|风险收益平衡|付出与回报相当|有付出才有回报|风险与收益成正比)(?:{RISK_TYPE})?'
# 特殊表达
R3_SPECIAL = r'适度承担风险'

# 组合R3所有模式
R3_RISK_LEVEL_PATTERN = r'(?:' + '|'.join([
    R3_CORE,
    R3_LEVEL,
    R3_BALANCE_DESCRIPTOR,
    R3_RISK_RETURN_DESCRIPTOR,
    R3_SPECIAL
]) + r')'

# R4 风险等级（中高风险）：风险偏高，需要谨慎
# 以"风险"、"波动"、"起伏"等风险相关词为核心
R4_CORE = rf'''
(?:
    (?:
        {RISK_SLIGHTLY}
        {RISK_HIGH}
        的?
        {RISK_BASIC}
    )
    |
    (?:
        {RISK_HIGH}
        (?:一?点|一?些)
        的?
        {RISK_BASIC}
    )
    |
    (?:
        {RISK_BASIC}
        {RISK_SLIGHTLY}
        {RISK_HIGH}
    )
    |
    (?:
        {RISK_BASIC}
        {RISK_HIGH}
        (?:一?点|一?些)
    )
    |
    (?:
        {RISK_MIDDLE_UP}
        的?
        {RISK_BASIC}
    )
    |
    (?:
        {RISK_BASIC}
        {RISK_MIDDLE_UP}
    )
)'''
R4_LEVEL = rf'''
(?:
    (?:
        {RISK_LEVEL_DESCRIPTOR}
        {RISK_IS}
        (?:R4|r4|4|四)
        级?
        的?
    )
    |
    (?:
        (?:R4|r4)
        {RISK_LEVEL_DESCRIPTOR}?
    )
)'''
# 不稳定性描述
R4_UNSTABLE_DESCRIPTOR = r'(?:不太稳定|略显波动|有些不稳|波动较大|涨跌幅度大|震荡明显|起伏较大|上下起伏|震荡较大|有明显浮动|摇摆不定|有些颠簸|忽高忽低|高低不平|摇摇晃晃|跳跃性大|冲高回落|低开高走|高开低走)(?:的|得|型)?'
# 需要承担风险描述
R4_RISK_TOLERANCE_DESCRIPTOR = r'(?:需要承担风险|需要冒险|风险承受度高|接受较大风险|能接受波动|能承受震荡|风险承受力要求高|风险接受度要求高|需要抗风险|需要扛得住|要有心理准备|可能导致亏损|有可能造成损失|账面波动较大|资金可能缩水|可能需要补仓|可能被强平|可能会被锁仓)(?:的|得|型)?'
# 特殊表达
R4_SPECIAL = r'需要经验|进阶型|高于普通|不适合保守型|不适合稳健型|需要有经验|建议谨慎|谨慎参与|小仓位参与|不建议重仓|适合有经验的人|上升趋势中|风险收益倾斜|收益有可能高|可能会有较大收益|可能会亏|短期可能回撤|可接受风险人士|进取型|成长型|需要盯盘|睡觉可能会惊醒|不定期检查|需要关注|需要时刻留意|刺激但可控|半夜可能惊醒|坐过山车的感觉|有惊无险|走钢丝但有保护网'

# 组合R4所有模式
R4_RISK_LEVEL_PATTERN = r'(?:' + '|'.join([
    R4_CORE,
    R4_LEVEL,
    R4_UNSTABLE_DESCRIPTOR,
    R4_RISK_TOLERANCE_DESCRIPTOR,
    R4_SPECIAL
]) + r')'

# R5 风险等级（高风险）：风险极高，需要特别谨慎
# 以"风险"、"波动"、"起伏"等风险相关词为核心

R5_CORE = rf'''
(?:
    (?:
        {RISK_EXTREME}?
        {RISK_HIGH}
        的?
        {RISK_BASIC}
    )
    |
    (?:
        {RISK_BASIC}
        {RISK_EXTREME}?
        {RISK_HIGH}
    )
    |
    (?:
        {RISK_BASIC}
        (?:不|不能|无法|没发|没办法)
        {RISK_CONTROLLABLE}
    )
)'''
R5_LEVEL = rf'''
(?:
    (?:
        {RISK_LEVEL_DESCRIPTOR}
        {RISK_IS}
        (?:R5|r5|5|五)
        级?
        的?
    )
    |
    (?:
        (?:R5|r5)
        {RISK_LEVEL_DESCRIPTOR}?
    )
)'''
# 风险激进描述
R5_AGGRESSIVE_DESCRIPTOR = r'(?:激进|进取|冒险|投机|博弈|赌博|投钱|梭哈|冲刺|押宝|豪赌|孤注一掷|破釜沉舟|背水一战|放手一博|铤而走险|不保守|大胆|勇猛|无畏|敢闯|亡命|敢干|拼一把|玩命|赌命|置之死地|不顾后果|无所顾忌|鱼死网破|同归于尽|最后的搏命|不计代价)(?:的|得|型)?'
# 大幅波动描述
R5_VOLATILITY_DESCRIPTOR = r'(?:大幅波动|剧烈波动|高强度波动|极端震荡|剧烈震荡|狂风暴雨|过山车|跌宕起伏|乾坤大挪移|翻江倒海|九天揽月|五洋捉鳖|生死时速|极限运动|暴涨暴跌|过度波动|狂风巨浪|天翻地覆|地动山摇|惊涛骇浪|飞流直下|一泻千里|飞流直上|一飞冲天|大开大合|跌跌撞撞|颠三倒四|飘忽不定|无规律波动)(?:的|得|型)?'
# 特殊表达
R5_SPECIAL = r'抗风险能力要求极高|只适合专业人士|风险自担|后果自负|仅限专业|不建议普通人|需谨慎决策|大跌大涨|血亏血赚|九死一生|专业玩家|听天由命|看天吃饭|刀口舔血|玩火|走钢丝|可能爆仓|可能清零|可能一夜暴富|一掷千金|输光家底|富贵险中求|一飞冲天|祝你好运|可能归零|九输一赢|全凭运气|全看运气|十赌九输|风险无上限|风险不设限|睡不着觉|夜不能寐|提心吊胆|惴惴不安|心惊肉跳|胆战心惊|忐忑不安|如履薄冰|担惊受怕|诚惶诚恐|胆颤心惊|心神不宁|坐立不安|寝食难安'

# 组合R5所有模式
R5_RISK_LEVEL_PATTERN = r'(?:' + '|'.join([
    R5_CORE,
    R5_LEVEL,
    R5_AGGRESSIVE_DESCRIPTOR,
    R5_VOLATILITY_DESCRIPTOR,
    R5_SPECIAL
]) + r')'


# 新的风险等级表达式组合所有详细模式
RISK_LEVEL_EXPR = rf'''
(?:
    (?:{R2_RISK_LEVEL_PATTERN})
    |
    (?:{R4_RISK_LEVEL_PATTERN})
    |
    (?:{R3_RISK_LEVEL_PATTERN})
    |
    (?:{R1_RISK_LEVEL_PATTERN})
    |
    (?:{R5_RISK_LEVEL_PATTERN})
)
'''
    
# 风险等级超级模式
RISK_SUPER_PATTERN_STR = fr'''
(?: 
  # 近似前缀修饰词（可选）
  (?P<approximate_prefix>{RISK_APPROXIMATE_PREFIX})?
  
  # 上限前缀修饰词（可选）
  (?P<upper_limit_prefix>{RISK_UPPER_LIMIT_PREFIX})?
  
  # 下限前缀修饰词（可选）
  (?P<lower_limit_prefix>{RISK_LOWER_LIMIT_PREFIX})?
  
  (?:
    # 格式1：风险等级+连接词+风险等级
    (?P<risk_level1>{RISK_LEVEL_EXPR})
    (?P<connector>{RISK_RANGE_CONNECTOR})
    (?P<risk_level2>{RISK_LEVEL_EXPR})
    |
    # 格式2：单个风险等级
    (?P<risk_level>{RISK_LEVEL_EXPR})
  )
  
  # 上限后缀修饰词（可选）
  (?P<upper_limit_suffix>{RISK_UPPER_LIMIT_SUFFIX})?
  
  # 下限后缀修饰词（可选）
  (?P<lower_limit_suffix>{RISK_LOWER_LIMIT_SUFFIX})?
  
  # 近似后缀修饰词（可选）
  (?P<approximate_suffix>{RISK_APPROXIMATE_SUFFIX})?
)
'''

RISK_SUPER_PATTERN_COMPILED = re.compile(RISK_SUPER_PATTERN_STR, re.VERBOSE)

# 预编译的所有正则表达式字典
COMPILED_RISK_PATTERNS = {
    f"{RISK_PREFIX}-super_pattern": RISK_SUPER_PATTERN_COMPILED,
    f"{RISK_PREFIX}-range_connector": re.compile(RISK_RANGE_CONNECTOR),
    f"{RISK_PREFIX}-approximate_prefix": re.compile(RISK_APPROXIMATE_PREFIX),
    f"{RISK_PREFIX}-approximate_suffix": re.compile(RISK_APPROXIMATE_SUFFIX),
    f"{RISK_PREFIX}-upper_limit_prefix": re.compile(RISK_UPPER_LIMIT_PREFIX),
    f"{RISK_PREFIX}-upper_limit_suffix": re.compile(RISK_UPPER_LIMIT_SUFFIX),
    f"{RISK_PREFIX}-lower_limit_prefix": re.compile(RISK_LOWER_LIMIT_PREFIX),
    f"{RISK_PREFIX}-lower_limit_suffix": re.compile(RISK_LOWER_LIMIT_SUFFIX),
}

if __name__ == "__main__":
    import sys
    import os

    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(script_dir))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from resource.update_helper import update_patterns_to_es
    update_patterns_to_es(COMPILED_RISK_PATTERNS, "tool_regexs")