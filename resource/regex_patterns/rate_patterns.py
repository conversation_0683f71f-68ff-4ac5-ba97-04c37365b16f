import re
import os
import sys
from typing import List

if __name__ == "__main__":
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(script_dir))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
        
from resource.regex_patterns.num_patterns import COMPLEX_NUMBER_PATTERN

RATE_PREFIX = "rate"

# 收益率单位定义
class RateUnits:
    """收益率单位定义类"""
    
    # 收益率单位映射（原始单位 -> 标准单位）
    UNIT_MAPPING = {
        # 百分比 
        "%": "percent", "％": "percent", "个百分点": "percent", "百分点": "percent", "个点": "percent", "点几": "percent", "点多": "percent",
        # 百分比
        "个点儿": "percent", 
        # 千分比
        "‰": "permille", "千分点": "permille",
        # 基点
        "BP": "basic_point", "bp": "basic_point", "个基点": "basic_point", "基点": "basic_point",
        # 点
        "点": "point", 
    }
    
    # 单位转换为小数比例
    DECIMAL_CONVERSION = {
        "percent": 0.01,     # 百分比：除以100
        "permille": 0.001,   # 千分比：除以1000
        "basic_point": 0.0001, # 基点：除以10000
        "point": 0.01,
    }
    
    @classmethod
    def get_all_units(cls) -> List[str]:
        """获取所有收益率单位"""
        return list(cls.UNIT_MAPPING.keys())
    
    @classmethod
    def get_standard_unit(cls, unit: str) -> str:
        """获取标准化的收益率单位"""
        return cls.UNIT_MAPPING.get(unit, "percent")
    
    @classmethod
    def get_unit_pattern(cls) -> str:
        """获取收益率单位的正则表达式模式"""
        return f"({'|'.join(cls.get_all_units())})"

# 收益率单位模式
RATE_UNIT_PATTERN = RateUnits.get_unit_pattern()

# 定义基本收益率单元模式
RATE_UNIT_EXPR = fr'''
(?:
  (?:{COMPLEX_NUMBER_PATTERN}\s*{RATE_UNIT_PATTERN})
)
'''

# 年化收益率上下文关键词（去掉'年收益'、'年度收益'的匹配，容易与"一年收益率xx"、"一年度收益xx"等时间表达"一年"重叠"年"字符）
RATE_CONTEXT_KEYWORDS = r'''
(
    # 年化收益相关词（可选）
    (?:年度|年化)?
    # 预期词
    (?:预期|预计|预估|预测|目标|期望)?
    # 投资理财词
    (?:投资|理财|基金|股票|债券)?
    # 存款词
    (?:定期|活期)?(?:存款|储蓄|存单)?
    # 其他词
    (?:固定|参考|基准|参考|历史|过往)?
    # 收益率表达形式
    (?:收益率|收益|利息|利率|回报率?)
)
'''

# 非收益率上下文关键词（用于过滤非收益率需求表达）
NON_RATE_CONTEXT_KEYWORDS = r'''
(
    # 风险指标类
    最大回撤|回撤|最大回撤率|回撤率|
    最大亏损|亏损|最大亏损率|亏损率|
    波动率|波动|标准差|波动幅度|波动区间|
    风险|风险率|风险系数|风险指标|风险度量|
    # 业绩评价指标
    夏普比率|夏普|夏普指标|夏普系数|
    贝塔系数|贝塔|Beta系数|系统性风险|
    阿尔法系数|阿尔法|Alpha系数|超额收益率|
    索提诺比率|索提诺|特雷诺比率|特雷诺|
    卡玛比率|卡玛|
    # 补充非收益率指标
    换手率|换手|周转率|周转|
    成交率|成交量|成交额|
    涨跌幅|涨幅|跌幅|涨跌比率|
    市盈率|市净率|市销率|
    增长率|增速|增长幅度|
    占比|占比率|比重|权重|
    费率|费用率|管理费率|托管费率|
    折价率|溢价率|折溢价率|
    胜率|盈利率|亏损率|成功率
)
'''

# 收益率范围连接词
# 按符号类型分组
RANGE_TEXT_CONNECTORS = r'到|至|和|与|或|及'
RANGE_DASH_CONNECTORS = r'-|—|－|–|‐|‑|‒|⸺|⸻|﹘|﹣|⹀'
RANGE_TILDE_CONNECTORS = r'~|⁓|∼|～|∿|〜|〰'
RANGE_ARROW_CONNECTORS = r'→|➝|➞|⟶|⇒|⟹|⇾|⟾|⟿'
RANGE_OTHER_SYMBOLS = r'、|_|⁃|…|=|==|:|\\+|±'  # 转义加号
RANGE_COMPOUND_SYMBOLS = r'->|-->|=>'

# 收益率范围连接词
RATE_RANGE_CONNECTOR = r'(' + '|'.join([
    RANGE_TEXT_CONNECTORS,
    RANGE_DASH_CONNECTORS,
    RANGE_TILDE_CONNECTORS,
    RANGE_ARROW_CONNECTORS,
    RANGE_OTHER_SYMBOLS,
    RANGE_COMPOUND_SYMBOLS
]) + r')'

# 近似修饰前缀：出现在收益率前的近似修饰词
# 调整为更适合收益率场景的近似词
PREFIX_RATE_APPROXIMATION = r'大约|大概|约|约为|粗略|大致|大致上|大体|概|粗略估计|姑且|权且|暂且'
PREFIX_RATE_PROXIMITY = r'接近|差不多|近|将近|几乎|接近于|濒临|靠近|邻近|左近|附近'
PREFIX_RATE_ESTIMATION = r'估计|预期|预计|预测|目标|粗算|算下来|看起来|算起来|目测|估摸|估量|佔计|目估|初步估计'
PREFIX_RATE_PREDICTION = r'预计|预估|预期|预测|可能|或许|也许|兴许|恐怕|大抵|想来|想必|料想|多半'
PREFIX_RATE_EQUIVALENCE = r'相当于|等同于|等于|等价于|好比|好像|仿佛|宛如|比如|相当于是|大约等于'
PREFIX_RATE_UNCERTAINTY = r'也就|大体上|基本|基本上|大体来说|总体来讲|笼统地说|大略|凡是|大体而言'

RATE_APPROXIMATE_PREFIX = r'(' + '|'.join([
    PREFIX_RATE_APPROXIMATION,
    PREFIX_RATE_PROXIMITY,
    PREFIX_RATE_ESTIMATION,
    PREFIX_RATE_PREDICTION,
    PREFIX_RATE_EQUIVALENCE,
    PREFIX_RATE_UNCERTAINTY
]) + r')'

# 近似修饰后缀：出现在收益率后的近似修饰词
# 调整为更适合收益率场景的近似词
SUFFIX_RATE_RANGE = r'左右|上下|内外|之间|前后|区间|附近|范围|浮动|波动'
SUFFIX_RATE_FLUCTUATION = r'上下浮动|摆布|上下波动|起伏|波动|变动|不稳定|波动区间'
SUFFIX_RATE_UNCERTAINTY = r'不等|不定|变化|不一|不确定|待定|未定|不稳定|浮动'
SUFFIX_RATE_EXCESS = r'出头|出头儿|开外|多一点|有余|略多|稍多|略微超过|略超|略高|偏多'
SUFFIX_RATE_APPROXIMATION = r'几|许|多|大概|大约|估计|约摸|预计|或者说|可以说|要么|要不|姑且算是'

RATE_APPROXIMATE_SUFFIX = r'(' + '|'.join([
    SUFFIX_RATE_RANGE,
    SUFFIX_RATE_FLUCTUATION,
    SUFFIX_RATE_UNCERTAINTY,
    SUFFIX_RATE_EXCESS,
    SUFFIX_RATE_APPROXIMATION
]) + r')'

# 上限前缀修饰词：放在收益率前，表示收益率的最大限制
# 移除不适合收益率的词语，增加收益率专用词语
UPPER_LIMIT_RATE_NEGATION = r'不超过|不到|不足|不会超过|不高于|不会多于|不能超过|不得超过|不应超过|不可超过|不能高于|不超過|不到|不足|不会超过|不会超過|不高于|不高於|不会多于|不会多於|不能超过|不能超過|不得超过|不应超过|不可超过|不能高于|不应高于'
UPPER_LIMIT_RATE_COMPARISON = r'少于|低于|小于|少於|低於|小于|小於'
UPPER_LIMIT_RATE_MAX = r'最多|顶多|頂多|至多|最高|最大|最高不超过|最大不超|最多不超过|最高不超|最大不超过|封顶|最高收益|最大收益|最高回报|封顶收益'
UPPER_LIMIT_RATE_LOW = r'低到|能力低到|表现低到|水平低到|增长低到|回报低到|盈利低到|效益低到|弱到|糟糕到'
UPPER_LIMIT_RATE_BOUNDARY = r'上限是|上限为|封顶|最高限额|最大值为|最高收益率|最大收益率'
UPPER_LIMIT_RATE_FINANCE = r'预期收益上限|参考收益上限|目标收益上限|最高预期收益|最高目标收益'
UPPER_LIMIT_RATE_SYMBOLS = r'≤|<=|≦|<|＜'

RATE_UPPER_LIMIT_PREFIX = r'(' + '|'.join([
    UPPER_LIMIT_RATE_NEGATION,
    UPPER_LIMIT_RATE_COMPARISON,
    UPPER_LIMIT_RATE_MAX,
    UPPER_LIMIT_RATE_LOW,
    UPPER_LIMIT_RATE_BOUNDARY,
    UPPER_LIMIT_RATE_FINANCE,
    UPPER_LIMIT_RATE_SYMBOLS
]) + r')'

# 上限后缀修饰词：放在收益率后，表示收益率的最大限制
# 调整为更适合收益率场景的上限词
SUFFIX_RATE_BOUNDARY = r'以下|以内|之内|及以下|及以内|及之内|未满|之前|以前|不到|顶|为止|为限|止|之下|范围内|上限'
SUFFIX_RATE_CAP = r'封顶|封顶线|封顶值|封顶收益|最高收益|最高回报|最高收益率|最大收益率'
SUFFIX_RATE_COMPARISON = r'或更少|及更少|或以下|及以下|或更低|及更低|或少于此数|以下|更低|更少|不超|最多|最高|最大'
SUFFIX_RATE_RESTRICTION = r'限制|上限|天花板|最高值|最大值|不会更高|上限值'
SUFFIX_RATE_SYMBOLS = r'↓'

RATE_UPPER_LIMIT_SUFFIX = r'(' + '|'.join([
    SUFFIX_RATE_BOUNDARY,
    SUFFIX_RATE_CAP,
    SUFFIX_RATE_COMPARISON,
    SUFFIX_RATE_RESTRICTION,
    SUFFIX_RATE_SYMBOLS
]) + r')'

# 下限前缀修饰词：放在收益率前，表示收益率的最小限制
# 调整为更适合收益率场景的下限词
LOWER_LIMIT_RATE_MINIMUM = r'最少|至少|起码|最低|至少要|底线|保底|最低限度|保本|保障|起码有'
LOWER_LIMIT_RATE_NEGATION = r'不少于|不少於|不低于|不低於|不会少于|不会少於|不能少于|不能少於|不小于|不小於|不得少于|不应少於'
LOWER_LIMIT_RATE_COMPARISON = r'多于|多於|高于|高於|大于|大於|超|超过|超出|多过|远超|高出|大过|大于等于'
LOWER_LIMIT_RATE_HIGH = r'高到|好到|能力高到|表现好到|水平高到|增长到|回报好到|回报高到|盈利好到|盈利高到|达到|效益好到|强到|优秀到'
LOWER_LIMIT_RATE_BOUNDARY = r'下限是|底线是|起始于|开始于|基准是|基线是|保底收益|最低收益|最低回报'
LOWER_LIMIT_RATE_FINANCE = r'保本收益|保证收益|最低预期|基准收益|保底利率|最低保障|最低年化'
LOWER_LIMIT_RATE_SYMBOLS = r'>|≥|>=|＞|≧|⩾'

RATE_LOWER_LIMIT_PREFIX = r'(' + '|'.join([
    LOWER_LIMIT_RATE_MINIMUM,
    LOWER_LIMIT_RATE_NEGATION,
    LOWER_LIMIT_RATE_COMPARISON,
    LOWER_LIMIT_RATE_HIGH,
    LOWER_LIMIT_RATE_BOUNDARY,
    LOWER_LIMIT_RATE_FINANCE,
    LOWER_LIMIT_RATE_SYMBOLS
]) + r')'

# 下限后缀修饰词：放在收益率后，表示收益率的最小限制
# 调整为更适合收益率场景的下限词
SUFFIX_RATE_BOUNDARY_LOWER = r'以上|之上|及以上|及之上|起|开始|为基础|为底|为下限'
SUFFIX_RATE_FLOOR = r'最少|最低|底线|至少|保底|起码|保障|基准|下限|最低值'
SUFFIX_RATE_DIRECTION = r'往上|向上|以上区间|以上水平|上浮|走高|看涨|看高|增长'
SUFFIX_RATE_COMPARISON_LOWER = r'或更多|及更多|或以上|及更高|甚至更高|乃至更多|不止|更高|更多'
SUFFIX_RATE_FINANCE_THRESHOLD = r'保本|保障|保证|最低保证|基准收益|参考收益|最低预期|最低目标'
SUFFIX_RATE_SYMBOLS_LOWER = r'↑|⬆|➚|↗|⥣|⟰|⇧|⇑'

RATE_LOWER_LIMIT_SUFFIX = r'(' + '|'.join([
    SUFFIX_RATE_BOUNDARY_LOWER,
    SUFFIX_RATE_FLOOR,
    SUFFIX_RATE_DIRECTION,
    SUFFIX_RATE_COMPARISON_LOWER,
    SUFFIX_RATE_FINANCE_THRESHOLD,
    SUFFIX_RATE_SYMBOLS_LOWER
]) + r')'

# 否定前缀：表示对后续收益率表达的否定
RATE_NEGATION_PREFIX = r'(不能|不会|不应该|不应|不可以|不可|不能够|不允许|不准|不得|禁止|严禁|不宜|不要|切忌|切莫|切勿|别|莫|勿|毋)'

# 收益率超级模式：统一匹配各类收益率表达式
RATE_SUPER_PATTERN_STR = fr'''
(?: 
  # 非收益上下文词（可选，用于后续过滤）
  (?P<non_rate_expression>{NON_RATE_CONTEXT_KEYWORDS})?
  
  # 收益率上下文词（可选）
  (?P<rate_context_prefix>{RATE_CONTEXT_KEYWORDS})?
  
  # 实际的收益率表达式部分（核心收益率部分）
  (?P<actual_rate_expression>
  
    # 否定前缀（可选）
    (?P<negation_prefix>{RATE_NEGATION_PREFIX})?
    
    # 近似前缀修饰词（可选）
    (?P<approximate_prefix>{RATE_APPROXIMATE_PREFIX})?
    
    # 上限前缀修饰词（可选）
    (?P<upper_limit_prefix>{RATE_UPPER_LIMIT_PREFIX})?
    
    # 下限前缀修饰词（可选）
    (?P<lower_limit_prefix>{RATE_LOWER_LIMIT_PREFIX})?
    
    # 连接介词（非捕获，可选）
    (?:\s*(?:为|是|达|达到|可达|在|大概|大约|有|保持在|高达|等于|维持在|处于|:|：|：\s*)?\s*)?
    
    (?:
      # 格式1：收益率单元+连接词+收益率单元
      (?P<rate_unit1>{RATE_UNIT_EXPR})
      \s*(?P<connector>{RATE_RANGE_CONNECTOR})\s*
      (?P<rate_unit2>{RATE_UNIT_EXPR})
      |
      # 格式2：数字+连接词+数字+单位
      (?P<complex_num1>{COMPLEX_NUMBER_PATTERN})
      \s*(?P<connector2>{RATE_RANGE_CONNECTOR})\s*
      (?P<complex_num2>{COMPLEX_NUMBER_PATTERN})
      (?P<unit_pattern>{RATE_UNIT_PATTERN})?
      |
      # 格式3：单个收益率单元
      (?P<rate_unit>{RATE_UNIT_EXPR})
      |
      # 格式4：纯数字或百分比表达式(无单位，在收益率上下文中)
      (?P<rate_context_num>{COMPLEX_NUMBER_PATTERN})
    )
    
    # 上限后缀修饰词（可选）
    (?P<upper_limit_suffix>{RATE_UPPER_LIMIT_SUFFIX})?
    
    # 下限后缀修饰词（可选）
    (?P<lower_limit_suffix>{RATE_LOWER_LIMIT_SUFFIX})?
    
    # 近似后缀修饰词（可选）
    (?P<approximate_suffix>{RATE_APPROXIMATE_SUFFIX})?
  )
  # 的（可选）
  (?:\s*的\s*)?
  
  # 收益率上下文词（可选）
  (?P<rate_context_suffix>{RATE_CONTEXT_KEYWORDS})?
)
'''

RATE_SUPER_PATTERN_COMPILED = re.compile(RATE_SUPER_PATTERN_STR, re.VERBOSE)

# 预编译的所有正则表达式字典
COMPILED_RATE_PATTERNS = {
    f"{RATE_PREFIX}-super_pattern": RATE_SUPER_PATTERN_COMPILED,
    f"{RATE_PREFIX}-unit_pattern": re.compile(RATE_UNIT_PATTERN),
    f"{RATE_PREFIX}-range_connector": re.compile(RATE_RANGE_CONNECTOR),
    f"{RATE_PREFIX}-approximate_prefix": re.compile(RATE_APPROXIMATE_PREFIX),
    f"{RATE_PREFIX}-approximate_suffix": re.compile(RATE_APPROXIMATE_SUFFIX),
    f"{RATE_PREFIX}-upper_limit_prefix": re.compile(RATE_UPPER_LIMIT_PREFIX),
    f"{RATE_PREFIX}-upper_limit_suffix": re.compile(RATE_UPPER_LIMIT_SUFFIX),
    f"{RATE_PREFIX}-lower_limit_prefix": re.compile(RATE_LOWER_LIMIT_PREFIX),
    f"{RATE_PREFIX}-lower_limit_suffix": re.compile(RATE_LOWER_LIMIT_SUFFIX),
    f"{RATE_PREFIX}-negation_prefix": re.compile(RATE_NEGATION_PREFIX)
}

if __name__ == "__main__":
    from resource.update_helper import update_patterns_to_es
    update_patterns_to_es(COMPILED_RATE_PATTERNS, "tool_regexs")