"""
金额抽取器

专注于提取金融文本中的金额值

输入格式:
    text: str - 包含金额信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的金额信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,                 # 槽位名称
        "slot_value": str,       # 槽位值类型：'amount_range' 或 'specific_amount'
        # 对于 amount_range:
        "minValue": float,       # 范围最小值（元），可能为 None
        "maxValue": float,       # 范围最大值（元），可能为 None
        # 对于 specific_amount:
        "value": float           # 具体金额值（元）
    }
"""
import re
from typing import Dict, List, Any, Optional, Tuple
from app.utils.extractor.num.number_convert import text_to_num
from app.utils.extractor.num.num_utils import (
    AmountUnitHandler, AmountModifierHandler,
)
from app.utils.extractor.shared.shared_utils import create_result, logger
from app.services.cache_service import cache_service
from resource.regex_patterns.amount_patterns import (
    AMOUNT_PREFIX,
    AMOUNT_SUPER_PATTERN_COMPILED
)

class AmountExtractor:
    """
    金额抽取器
    专注于从文本中提取金额值信息（通常为元、万元等带有货币单位的值）
    """
    # 槽位类型
    SPECIFIC_AMOUNT = "specific_amount"
    AMOUNT_RANGE = "amount_range"
    
    # 纯数字被视为金额的最小阈值
    MIN_NUMBER_THRESHOLD = 100

    
    def extract_amount(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取金额信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的金额信息列表
        """
        try:
            results = []
            AMOUNT_SUPER_PATTERN = self._get_regex(tool_name="super_pattern", default_regex=AMOUNT_SUPER_PATTERN_COMPILED)

            # 处理统一金额超级模式
            for match in AMOUNT_SUPER_PATTERN.finditer(text):
                result = self.process_unified_amount(match)
                if result:
                    results.append(result)
            
            # 过滤和标准化提取的值
            return self._filter_values(results)
            
        except Exception as e:
            logger.warning(f"[AMOUNT_EXTRACTOR] Warning: {e.__class__.__name__}: {e}, 原始文本: '{text}'")
            return []
    
    def process_unified_amount(self, match) -> Optional[Dict[str, Any]]:
        """处理统一金额模式的匹配结果"""
        match_text = match.group(0)
        
        # 提取命名捕获组
        components = {name: value for name, value in match.groupdict().items() if value is not None}
        
        # 提取各类修饰词信息
        modifiers = self._extract_modifiers(match)
        
        # 处理不同的金额格式
        if 'amount_unit1' in components and 'amount_unit2' in components:
            return self._process_amount_range_with_units(match_text, components, match, modifiers)
        elif 'amount_unit' in components:
            return self._process_single_amount_unit(match_text, components, match, modifiers)
        elif 'number1' in components and 'number2' in components:
            return self._process_range_with_shared_unit(match_text, components, match, modifiers)
        elif 'number_only' in components:
            return self._process_number_only(match_text, components, match, modifiers)
                
        return None
    
    def _extract_modifiers(self, match) -> Dict[str, bool]:
        """提取各类修饰词信息"""
        has_upper_modifier = match.group('upper_limit_prefix') is not None or match.group('upper_limit_suffix') is not None
        has_lower_modifier = match.group('lower_limit_prefix') is not None or match.group('lower_limit_suffix') is not None
        has_negation_modifier = match.group('negation_prefix') is not None
        has_approximate_modifier = match.group('approximate_prefix') is not None or match.group('approximate_suffix') is not None
        
        # 判断是否有任何修饰词（不考虑近似修饰词）
        has_modifier = has_upper_modifier or has_lower_modifier or has_negation_modifier
        
        return {
            "has_upper": has_upper_modifier,
            "has_lower": has_lower_modifier,
            "has_negation": has_negation_modifier,
            "has_approximate": has_approximate_modifier,
            "has_modifier": has_modifier
        }
    
    def _process_amount_range_with_units(self, match_text: str, components: Dict, match, modifiers: Dict[str, bool]) -> Optional[Dict[str, Any]]:
        """处理带有两个金额单位的范围表达式"""
        value1 = AmountUnitHandler.normalize_amount(components['amount_unit1'])
        value2 = AmountUnitHandler.normalize_amount(components['amount_unit2'])
        min_value = min(value1, value2)
        max_value = max(value1, value2)
        
        return self._create_amount_range_result(match_text, min_value, max_value, match, modifiers)
    
    def _process_single_amount_unit(self, match_text: str, components: Dict, match, modifiers: Dict[str, bool]) -> Optional[Dict[str, Any]]:
        """处理单个金额单元"""
        value = AmountUnitHandler.normalize_amount(components['amount_unit'])
     
        # 如果没有修饰词，则认为是单一金额
        if not modifiers["has_modifier"]:
            return create_result(
                match=match_text,
                position=(match.start(), match.end()),
                slot="number",
                slot_value=self.SPECIFIC_AMOUNT,
                value=value
            )
        
        # 有修饰词的情况
        return self._create_amount_range_result(match_text, value, value, match, modifiers)
    
    def _process_range_with_shared_unit(self, match_text: str, components: Dict, match, modifiers: Dict[str, bool]) -> Optional[Dict[str, Any]]:
        """处理共享单位的范围表达"""
        number1 = text_to_num(components['number1'])
        number2 = text_to_num(components['number2'])
        unit = components.get('shared_unit', '元')
        
        # 如果数值为空，则返回 None
        if number1 is None or number2 is None:
            return None
        
        value1 = AmountUnitHandler.to_yuan(number1, unit)
        value2 = AmountUnitHandler.to_yuan(number2, unit)
        
        min_value = min(value1, value2)
        max_value = max(value1, value2)
        
        return self._create_amount_range_result(match_text, min_value, max_value, match, modifiers)
    
    def _process_number_only(self, match_text: str, components: Dict, match, modifiers: Dict[str, bool]) -> Optional[Dict[str, Any]]:
        """处理纯数字格式"""
        number_str = components['number_only']
        
        # 检查是否以单位开头
        for unit in ['万', '萬', 'w', "W"]:
            if number_str.lower().startswith(unit):
                return None
        
        # 检查匹配文本前一个字符是否为字母
        match_start = match.start()
        if match_start > 0 and match.string[match_start-1].isascii() and match.string[match_start-1].isalpha():
            return None
        
        value = text_to_num(number_str)
        
        # 如果数值为空，则返回 None
        if value is None:
            return None
        
        # 检查数值是否超过阈值
        if value < self.MIN_NUMBER_THRESHOLD:
            return None
        
        # 如果没有修饰词，并且数值足够大，认为是单一金额
        if not modifiers["has_modifier"]:
            return create_result(
                match=match_text,
                position=(match.start(), match.end()),
                slot="number",
                slot_value=self.SPECIFIC_AMOUNT,
                value=value
            )
        
        # 有修饰词的情况
        return self._create_amount_range_result(match_text, value, value, match, modifiers)
    
    def _create_amount_range_result(self, match_text: str, min_value: float, max_value: float, match, modifiers: Dict[str, bool]) -> Dict[str, Any]:
        """创建金额范围结果"""
        # 应用修饰词的影响，传递修饰词判断结果
        min_value, max_value = AmountModifierHandler.apply_modifiers(
            min_value=min_value if min_value is not None else max_value,
            max_value=max_value if max_value is not None else min_value,
            has_upper=modifiers["has_upper"],
            has_lower=modifiers["has_lower"],
            has_negation=modifiers["has_negation"],
            has_approximate=modifiers["has_approximate"]
        )
        
        # 创建结果
        return create_result(
            match=match_text,
            position=(match.start(), match.end()),
            slot="number",
            slot_value=self.AMOUNT_RANGE,
            min_value=min_value,
            max_value=max_value
        )
    
    def _filter_values(self, values: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤无效的金额值"""
        filtered_values = []
        
        for value in values:
            is_valid = False
            
            if value["slot_value"] == self.SPECIFIC_AMOUNT:
                # 对于具体金额，值必须大于0
                if value.get("value", 0) > 0:
                    is_valid = True
            elif value["slot_value"] == self.AMOUNT_RANGE:
                # 对于金额范围，至少有一个边界值且有效
                min_val = value.get("minValue")
                max_val = value.get("maxValue")
                
                if (min_val is not None and min_val >= 0) or (max_val is not None and max_val > 0):
                    # 如果两个值都存在，确保范围有效
                    if min_val is not None and max_val is not None:
                        if max_val >= min_val:
                            is_valid = True
                    else:
                        is_valid = True
            
            if is_valid:
                filtered_values.append(value)
                
        return filtered_values
    
    def _get_regex(self, tool_name: str, default_regex: str, slot_name: str = AMOUNT_PREFIX) -> re.Pattern:
        """获取正则表达式"""
        cached_regex = cache_service.get_tool_regexs(
            slot_name=slot_name,
            tool_name=tool_name
        )
        
        if not cached_regex:
            logger.warning(f"{tool_name} is None, use default pattern instead")
            # 如果默认模式是字符串则编译，已经是编译好的正则则直接使用
            return re.compile(default_regex) if isinstance(default_regex, str) else default_regex
        return cached_regex
