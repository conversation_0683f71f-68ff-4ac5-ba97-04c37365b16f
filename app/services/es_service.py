#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Elasticsearch用于槽位词库和槽位正则在多实例中同步存储、更新

此脚本用于在程序启动时，导入resource/regex_patterns下的各种编译好的正则表达式，并尝试添加到es中
同时，将关键词列表转换为正则表达式字符串后存储到ES中
ES中所有数据统一以正则表达式字符串格式存储

ES_Index: slot_extraction
槽位词库：
    id：keywords_<slot_key>
    type: keywords
    slot_name: 槽位名称
    slot_value: 槽位值
    data: 由关键词列表生成的正则表达式字符串
槽位正则：
    id: regexs_<slot_key>
    type: regexs
    slot_name: 槽位名称
    slot_value: 槽位值
    data: 正则表达式字符串
工具类正则（金额、收益率等）：
    id: tool_regexs_<prefix>_<tool_name>
    type: tool_regexs
    slot_name: 工具类别（amount、rate等）
    slot_value: 工具名称
    data: 正则表达式字符串
"""

import json
import importlib
import os
import re
import time
from typing import Dict, Any, List, Union, Optional, Tuple
from elasticsearch import Elasticsearch, exceptions

from app.settings import ESConfig
from app.utils.extractor.shared.shared_utils import logger
from resource.regex_patterns.consecutive_periods_patterns import COMPILED_CONSECUTIVE_PERIODS_PATTERNS
from resource.regex_patterns.rate_patterns import COMPILED_RATE_PATTERNS
from resource.regex_patterns.num_patterns import COMPILED_NUM_PATTERNS
from resource.regex_patterns.amount_patterns import COMPILED_AMOUNT_PATTERNS
from resource.regex_patterns.risk_patterns import COMPILED_RISK_PATTERNS
from resource.regex_patterns.time_duration_patterns import COMPILED_TIME_DURATION_PATTERNS

class ESService:
    """Elasticsearch服务类，用于管理槽位相关数据的存储和检索"""
    
    def __init__(self):
        self.REGEXS_PREFIX = "regexs"
        self.KEYWORDS_PREFIX = "keywords"
        self.TOOL_REGEXS_PREFIX = "tool_regexs"
        
        """初始化ES连接"""
        try:
            self.es = Elasticsearch(
                hosts=ESConfig.ES_HOSTS,
                basic_auth=(ESConfig.ES_USER, ESConfig.ES_PASSWORD) if ESConfig.ES_AUTH_REQUIRED else None,
                verify_certs=ESConfig.ES_VERIFY_CERTS,
                timeout=ESConfig.ES_TIMEOUT,
                retry_on_timeout=True,
                max_retries=ESConfig.ES_MAX_RETRIES
            )
            self.index_name = ESConfig.ES_INDEX_NAME
        except Exception as e:
            logger.error(f"初始化ES连接失败: {str(e)}")
            self.es = None
            self.index_name = None
        
    def is_connected(self) -> bool:
        """
        检查Elasticsearch连接是否正常
        
        Returns:
            bool: 连接是否正常
        """
        if not self.es:
            return False
            
        try:
            return self.es.ping()
        except Exception as e:
            logger.error(f"ES连接检查失败: {str(e)}")
            return False
    
    def reconnect(self) -> bool:
        """
        重新建立Elasticsearch连接
        
        Returns:
            bool: 重连是否成功
        """
        try:
            self.es = Elasticsearch(
                hosts=ESConfig.ES_HOSTS,
                basic_auth=(ESConfig.ES_USER, ESConfig.ES_PASSWORD) if ESConfig.ES_AUTH_REQUIRED else None,
                verify_certs=ESConfig.ES_VERIFY_CERTS,
                timeout=ESConfig.ES_TIMEOUT,
                retry_on_timeout=True,
                max_retries=ESConfig.ES_MAX_RETRIES
            )
            self.index_name = ESConfig.ES_INDEX_NAME
            logger.info("ES重新连接成功")
            return self.is_connected()
        except Exception as e:
            logger.error(f"ES重新连接失败: {str(e)}")
            return False
    
    def close(self):
        """
        关闭Elasticsearch连接
        """
        if self.es:
            try:
                self.es.close()
                logger.info("ES连接已关闭")
            except Exception as e:
                logger.error(f"关闭ES连接失败: {str(e)}")
            finally:
                self.es = None
    
    def _create_index_if_not_exists(self, force: bool = False) -> bool:
        """
        如果索引不存在，则创建索引；如果force=True，则删除已存在的索引并重新创建
        
        Args:
            force: 是否强制重建索引，默认为False
            
        Returns:
            bool: 创建是否成功
        """
        try:
            index_exists = self.es.indices.exists(index=self.index_name)
            
            # 如果索引已存在且force=True，则删除索引
            if index_exists and force:
                logger.warning(f"强制重建索引 {self.index_name}，正在删除已存在的索引...")
                self.es.indices.delete(index=self.index_name)
                index_exists = False
                logger.info(f"索引 {self.index_name} 已删除")
                # 等待索引删除完成
                time.sleep(5)
                
            if not index_exists:
                # 创建索引，使用 ES 6.8.2 兼容的映射格式
                mapping_settings = {
                    "settings": {
                        "number_of_shards": 1,
                        "number_of_replicas": 0
                    },
                    "mappings": {
                        "doc": {  # ES 6.x 需要指定文档类型
                            "properties": {
                                "type": {"type": "keyword"},
                                "slot_name": {"type": "keyword"},
                                "slot_value": {"type": "keyword"},
                                "data": {
                                    "type": "text",  # 文本类型，统一存储正则表达式字符串
                                    "fields": {
                                        "keyword": {"type": "keyword", "ignore_above": 256}
                                    }
                                }
                            }
                        }
                    }
                }
                
                self.es.indices.create(index=self.index_name, body=mapping_settings)
                logger.info(f"创建索引 {self.index_name} 成功")
            
            return True
        except exceptions.ElasticsearchException as e:
            logger.error(f"{'重建' if force else '创建'}索引失败: {str(e)}")
            return False
    
    def _load_keywords(self) -> Dict[str, Dict[str, List[str]]]:
        """
        从keywords.json文件加载关键词
        
        Returns:
            Dict[str, Dict[str, List[str]]]: 关键词字典
        """
        try:
            keywords_path = os.path.join("resource", "keywords", "keywords.json")
            with open(keywords_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载keywords.json失败: {str(e)}")
            return {}
    
    def _load_regex_patterns(self) -> Dict[str, str]:
        """
        加载普通槽位正则表达式模式
        
        Returns:
            Dict[str, str]: 正则表达式字符串字典
        """
        patterns = {}
        
        # 加载连续期模式
        consecutive_patterns = COMPILED_CONSECUTIVE_PERIODS_PATTERNS
        for pattern_id, pattern in consecutive_patterns.items():
            # 确保获取pattern字符串
            if hasattr(pattern, 'pattern'):
                patterns[pattern_id] = pattern.pattern
            else:
                patterns[pattern_id] = str(pattern)
        
        return patterns
    
    def _load_tool_regex_patterns(self) -> Dict[str, str]:
        """
        专门加载工具类正则表达式模式（金额、收益率、风险等级）
        
        Returns:
            Dict[str, str]: 工具类正则表达式字符串字典
        """
        patterns = {}
        # 加载数值正则模式
        num_patterns = COMPILED_NUM_PATTERNS
        for pattern_id, pattern in num_patterns.items():
            if hasattr(pattern, 'pattern'):
                patterns[pattern_id] = pattern.pattern
            else:
                patterns[pattern_id] = str(pattern)
                
        # 加载金额正则模式
        amount_patterns = COMPILED_AMOUNT_PATTERNS
        for pattern_id, pattern in amount_patterns.items():
            if hasattr(pattern, 'pattern'):
                patterns[pattern_id] = pattern.pattern
            else:
                patterns[pattern_id] = str(pattern)
        
        # 加载收益正则模式
        rate_patterns = COMPILED_RATE_PATTERNS
        for pattern_id, pattern in rate_patterns.items():
            if hasattr(pattern, 'pattern'):
                patterns[pattern_id] = pattern.pattern
            else:
                patterns[pattern_id] = str(pattern)
        
        # 加载风险等级正则模式
        risk_patterns = COMPILED_RISK_PATTERNS
        for pattern_id, pattern in risk_patterns.items():
            if hasattr(pattern, 'pattern'):
                patterns[pattern_id] = pattern.pattern
            else:
                patterns[pattern_id] = str(pattern)
        
        # 加载持续时间正则模式
        time_duration_patterns = COMPILED_TIME_DURATION_PATTERNS
        for pattern_id, pattern in time_duration_patterns.items():
            if hasattr(pattern, 'pattern'):
                patterns[pattern_id] = pattern.pattern
            else:
                patterns[pattern_id] = str(pattern)
                
        return patterns
    
    def _convert_keywords_to_regex(self, keywords: List[str]) -> str:
        """
        将关键词列表转换为正则表达式字符串
        
        Args:
            keywords: 关键词列表
            
        Returns:
            str: 正则表达式字符串
        """
        if not keywords:
            return r"(?!)"  # 不可能匹配的模式
            
        # 对关键词进行转义并添加边界
        escaped_keywords = [re.escape(keyword) for keyword in keywords]
        
        # 使用|连接所有关键词模式构成或关系的正则表达式
        pattern_str = "|".join(escaped_keywords)
        
        return pattern_str
    
    def _store_keywords(self, keywords: Dict[str, Dict[str, List[str]]]) -> bool:
        """
        将关键词转换为正则表达式字符串后存储到ES中
        
        Args:
            keywords: 关键词字典
            
        Returns:
            bool: 操作是否成功
        """
        success = True
        for slot_name, slot_values in keywords.items():
            for slot_value, words in slot_values.items():
                try:
                    # 将关键词列表转换为正则表达式字符串
                    regex_pattern_str = self._convert_keywords_to_regex(words)
                    # 存储正则表达式字符串
                    self.upsert_slot_data(slot_name, slot_value, self.KEYWORDS_PREFIX, regex_pattern_str)
                except Exception as e:
                    logger.error(f"处理并存储关键词 {slot_name}_{slot_value} 失败: {str(e)}")
                    success = False
        return success
    
    def _store_regex_patterns(self, patterns: Dict[str, str]) -> bool:
        """
        将普通槽位正则表达式模式存储到ES中
        
        Args:
            patterns: 正则表达式字典
            
        Returns:
            bool: 操作是否成功
        """
        success = True
        for pattern_id, pattern in patterns.items():
            try:
                # 确保pattern是字符串类型
                if hasattr(pattern, 'pattern'):
                    pattern = pattern.pattern
                
                # 普通槽位正则
                slot_name, slot_value = pattern_id.split("-")
                self.upsert_slot_data(slot_name, slot_value, self.REGEXS_PREFIX, pattern)
            except Exception as e:
                logger.error(f"存储普通槽位正则表达式 {pattern_id} 失败: {str(e)}")
                success = False
        return success
    
    def _store_tool_regex_patterns(self, patterns: Dict[str, str]) -> bool:
        """
        将工具类正则表达式模式存储到ES中
        
        Args:
            patterns: 工具类正则表达式字典
            
        Returns:
            bool: 操作是否成功
        """
        success = True
        for pattern_id, pattern in patterns.items():
            try:
                # 确保pattern是字符串类型
                if hasattr(pattern, 'pattern'):
                    pattern = pattern.pattern
                
                # 工具类正则模式
                prefix, tool_name = pattern_id.split('-')  # 获取前缀（amount或rate）
                self.upsert_slot_data(prefix, tool_name, self.TOOL_REGEXS_PREFIX, pattern)
            except Exception as e:
                logger.error(f"存储工具类正则表达式 {pattern_id} 失败: {str(e)}")
                success = False
        return success
    
    def init_es_data(self, force_rebuild_index: bool = False) -> bool:
        """
        初始化ES数据，在应用启动时调用
        
        Args:
            force_rebuild_index: 是否强制重建索引，默认为False
            
        Returns:
            bool: 初始化是否成功
        """
        logger.info("开始初始化ES数据...")
        
        # 检查ES连接
        if not self.is_connected():
            logger.warning("ES连接不可用，尝试重新连接...")
            if not self.reconnect():
                logger.error("ES连接失败，无法初始化数据")
                return False
        
        # 确保索引存在
        if not self._create_index_if_not_exists(force=force_rebuild_index):
            return False
        
        # 加载并处理关键词
        keywords = self._load_keywords()
        keywords_success = self._store_keywords(keywords)
        
        # 加载并存储普通槽位正则表达式
        regex_patterns = self._load_regex_patterns()
        regex_success = self._store_regex_patterns(regex_patterns)
        
        # 加载并存储工具类正则表达式
        tool_regex_patterns = self._load_tool_regex_patterns()
        tool_regex_success = self._store_tool_regex_patterns(tool_regex_patterns)
        
        if keywords_success and regex_success and tool_regex_success:
            logger.info("初始化ES数据完成")
            return True
        else:
            logger.warning("初始化ES数据部分失败")
            return False
    
    def upsert_slot_data(self, slot_name: str, slot_value: str, data_type: str, data: str) -> bool:
        """
        将槽位数据写入ES或更新已有数据
        
        Args:
            slot_name: 槽位名称
            slot_value: 槽位值
            data_type: 数据类型，"keywords"、"regexs"、"tool_regexs"
            data: 正则表达式字符串
            
        Returns:
            bool: 操作是否成功
        """
        try:
            doc_id = f"{data_type}_{slot_name}_{slot_value}"
            # 准备文档
            doc = {
                "type": data_type,
                "slot_name": slot_name,
                "slot_value": slot_value,
                "data": data
            }
            
            # 使用upsert操作，为ES 6.8.2添加doc_type参数
            self.es.update(
                index=self.index_name,
                doc_type="doc",  # ES 6.x 需要指定文档类型
                id=doc_id,
                refresh="wait_for",
                body={
                    "doc": doc,
                    "doc_as_upsert": True
                }
            )
            logger.info(f"更新/插入文档 {doc_id} 成功")
            return True
        except exceptions.ElasticsearchException as e:
            logger.error(f"更新/插入文档 {doc_id} 失败: {str(e)}")
            return False
    
    def get_data(self, doc_id: str) -> Dict[str, Any]:
        """
        从ES获取数据
        
        Args:
            doc_id: 文档ID
            
        Returns:
            Dict[str, Any]: 获取的文档内容，如果不存在则返回空字典
        """
        try:            
            response = self.es.get(
                index=self.index_name, 
                doc_type="doc",  # ES 6.x 需要指定文档类型
                id=doc_id
            )
            
            return response["_source"]
        except exceptions.NotFoundError:
            logger.warning(f"文档 {doc_id} 不存在")
            return {}
        except exceptions.ElasticsearchException as e:
            logger.error(f"获取文档 {doc_id} 失败: {str(e)}")
            return {}
    
    def delete_data(self, doc_id: str) -> bool:
        """
        删除ES中的数据
        
        Args:
            doc_id: 文档ID
            
        Returns:
            bool: 操作是否成功
        """
        try:
            self.es.delete(
                index=self.index_name, 
                doc_type="doc",  # ES 6.x 需要指定文档类型
                id=doc_id
            )
            logger.info(f"删除文档 {doc_id} 成功")
            return True
        except exceptions.NotFoundError:
            logger.warning(f"要删除的文档 {doc_id} 不存在")
            return False
        except exceptions.ElasticsearchException as e:
            logger.error(f"删除文档 {doc_id} 失败: {str(e)}")
            return False
    
    def get_all_data_by_type(self, data_type: str) -> Dict[str, str]:
        """
        获取特定类型的所有数据
        
        Args:
            data_type: 数据类型，"keywords"或"regexs"
            
        Returns:
            Dict[str, str]: 包含所有该类型数据的字典，格式为 {doc_id: regex_pattern_str}
        """
        try:
            # 使用query DSL查询指定类型的所有文档
            query = {
                "query": {
                    "term": {
                        "type": data_type
                    }
                },
                "size": 10000  # 适当设置大小，确保能获取所有数据
            }
            
            response = self.es.search(
                index=self.index_name, 
                doc_type="doc",  # ES 6.x 需要指定文档类型
                body=query
            )
            
            # 处理结果
            result = {}
            for hit in response["hits"]["hits"]:
                doc_id = hit["_id"]
                doc_data = hit["_source"]["data"]
                # 确保返回字符串
                result[doc_id] = doc_data if isinstance(doc_data, str) else str(doc_data)
                
            logger.info(f"成功获取{len(result)}条{data_type}类型的数据")
            return result
        except exceptions.ElasticsearchException as e:
            logger.error(f"获取{data_type}类型数据失败: {str(e)}")
            return {}
    
    def get_data_by_slot(self, data_type: str, slot_name: Optional[str] = None, slot_value: Optional[str] = None) -> Dict[str, str]:
        """
        根据槽位名称和槽位值获取数据
        
        Args:
            data_type: 数据类型，"keywords"或"regexs"
            slot_name: 槽位名称，可选
            slot_value: 槽位值，可选
            
        Returns:
            Dict[str, str]: 包含符合条件的数据的字典，格式为 {doc_id: regex_pattern_str}
        """
        try:
            # 构建查询条件
            must_conditions = [{"term": {"type": data_type}}]
            
            if slot_name:
                must_conditions.append({"term": {"slot_name": slot_name}})
            
            if slot_value:
                must_conditions.append({"term": {"slot_value": slot_value}})
            
            query = {
                "query": {
                    "bool": {
                        "must": must_conditions
                    }
                },
                "size": 10000
            }
            
            response = self.es.search(
                index=self.index_name,
                doc_type="doc",  # ES 6.x 需要指定文档类型
                body=query
            )
            
            # 处理结果
            result = {}
            for hit in response["hits"]["hits"]:
                doc_id = hit["_id"]
                doc_data = hit["_source"]["data"]
                # 确保返回字符串
                result[doc_id] = doc_data if isinstance(doc_data, str) else str(doc_data)
            
            condition_str = f"类型={data_type}"
            if slot_name:
                condition_str += f", 槽位名={slot_name}"
            if slot_value:
                condition_str += f", 槽位值={slot_value}"
            
            logger.info(f"成功获取{len(result)}条符合条件({condition_str})的数据")
            return result
        except exceptions.ElasticsearchException as e:
            logger.error(f"获取{data_type}类型数据失败: {str(e)}")
            return {}
    
    def get_tool_regexs(self, tool_type: Optional[str] = None) -> Dict[str, str]:
        """
        获取工具类正则数据
        
        Args:
            tool_type: 工具类型（如'amount'或'rate'或'risk'），可选
            
        Returns:
            Dict[str, str]: 包含符合条件的工具类正则的字典，格式为 {doc_id: regex_pattern_str}
        """
        try:
            # 构建查询条件
            must_conditions = [{"term": {"type": self.TOOL_REGEXS_PREFIX}}]
            
            if tool_type:
                must_conditions.append({"term": {"slot_name": tool_type}})
            
            query = {
                "query": {
                    "bool": {
                        "must": must_conditions
                    }
                },
                "size": 10000
            }
            
            response = self.es.search(
                index=self.index_name,
                doc_type="doc",  # ES 6.x 需要指定文档类型
                body=query
            )
            
            # 处理结果
            result = {}
            for hit in response["hits"]["hits"]:
                doc_id = hit["_id"]
                doc_source = hit["_source"]
                doc_data = doc_source["data"]
                tool_type = doc_source["slot_name"]
                tool_name = doc_source["slot_value"]
                
                # 使用更友好的键格式：工具类型-工具名称
                friendly_key = f"{tool_type}-{tool_name}"
                # 确保返回字符串
                result[friendly_key] = doc_data if isinstance(doc_data, str) else str(doc_data)
            
            condition_str = f"类型={self.TOOL_REGEXS_PREFIX}"
            if tool_type:
                condition_str += f", 工具类型={tool_type}"
            
            logger.info(f"成功获取{len(result)}条符合条件({condition_str})的工具类正则数据")
            return result
        except exceptions.ElasticsearchException as e:
            logger.error(f"获取工具类正则数据失败: {str(e)}")
            return {}

# 单例模式
es_service = ESService()
